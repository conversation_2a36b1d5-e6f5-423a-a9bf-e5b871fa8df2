# MAIAChat Desktop - Executable Build Guide

This guide will help you convert the MAIAChat Desktop application into a standalone executable file that can be distributed and run on Windows systems without requiring Python installation.

## Overview

The build process creates:
- **MAIAChat.exe** - The main executable file
- **Supporting folder structure** - All necessary files and dependencies
- **User data preservation** - Knowledge base, conversations, and settings

## Prerequisites

### 1. Python Environment
- Python 3.8 or higher
- All application dependencies installed
- Virtual environment recommended

### 2. Build Tools
Install the build requirements using one of these methods:

**Option A: Automated installer (Recommended)**
```bash
python install_build_deps.py
```

**Option B: Manual installation**
```bash
pip install -r requirements-build-minimal.txt
```

**Option C: Direct PyInstaller installation**
```bash
pip install pyinstaller>=5.0.0
```

### 3. System Requirements
- Windows 10/11 (for Windows executable)
- At least 4GB free disk space for build process
- 8GB+ RAM recommended for large applications

## Quick Start

### Option 1: Automated Build (Recommended)
```bash
python build_exe.py
```

This script will:
- Check all dependencies
- Create the PyInstaller configuration
- Build the executable
- Create user documentation
- Clean up build artifacts

### Option 2: Manual Build
If you prefer manual control:

1. **Install PyInstaller**:
   ```bash
   pip install pyinstaller
   ```

2. **Create the executable**:
   ```bash
   pyinstaller --onedir --windowed --name MAIAChat main.py
   ```

3. **Add data files** (see detailed instructions below)

## Detailed Build Process

### Step 1: Prepare Your Environment

1. **Clean your workspace**:
   ```bash
   # Remove any previous builds
   rmdir /s dist
   rmdir /s build
   ```

2. **Verify all dependencies**:
   ```bash
   pip check
   python -c "import main_window; print('All imports successful')"
   ```

### Step 2: Configure the Build

The `build_exe.py` script includes comprehensive configuration:

- **Application metadata** (name, version, description)
- **Data files inclusion** (UI, handlers, configs)
- **Hidden imports** (ML libraries, PyQt6 components)
- **User data preservation** (knowledge base, conversations)

### Step 3: Execute the Build

Run the build script:
```bash
python build_exe.py
```

**Expected output**:
```
Building MAIAChat executable...
==================================================
Checking build dependencies...
✓ PyInstaller 6.x.x found
✓ All dependencies checked
Preparing build environment...
✓ Build environment prepared
Creating version info file...
✓ Version info file created
Creating PyInstaller spec file...
✓ Spec file MAIAChat.spec created
Building executable...
Running: python -m PyInstaller --clean --noconfirm MAIAChat.spec
✓ Executable built successfully!
Creating user guide...
✓ User guide created
Creating batch launcher...
✓ Batch launcher created
Cleaning up build artifacts...
✓ Build artifacts cleaned up
==================================================
✓ Build completed successfully!
✓ Executable location: dist/MAIAChat/
✓ Run: dist/MAIAChat/MAIAChat.exe
```

### Step 4: Test the Executable

1. **Navigate to the output directory**:
   ```bash
   cd dist/MAIAChat
   ```

2. **Test the executable**:
   ```bash
   MAIAChat.exe
   ```

3. **Verify functionality**:
   - Application starts without errors
   - UI loads correctly
   - API connections work
   - RAG functionality operates
   - Settings are preserved

## Output Structure

After successful build, you'll have:

```
dist/MAIAChat/
├── MAIAChat.exe                 # Main executable
├── _internal/                   # PyInstaller dependencies
│   ├── *.dll                   # System libraries
│   ├── *.pyd                   # Python extensions
│   └── ...                     # Other dependencies
├── knowledge_base/              # User's knowledge base
├── conversation_history/        # Chat history
├── profiles/                    # Agent profiles
├── config.json                  # Application settings
├── api_keys.json               # API configurations
├── USER_GUIDE.txt              # User documentation
└── Start_MAIAChat.bat          # Batch launcher
```

## Distribution

### What to Include
Distribute the **entire `MAIAChat` folder**, including:
- The executable file
- All supporting files and folders
- User documentation

### What NOT to Include
- Build artifacts (`build/` folder)
- Source code (unless desired)
- Virtual environment files
- Development tools

### Packaging Options

1. **ZIP Archive** (Recommended):
   ```bash
   # Create a ZIP file of the entire folder
   powershell Compress-Archive -Path "dist/MAIAChat" -DestinationPath "MAIAChat-v1.0.0.zip"
   ```

2. **Installer Creation** (Advanced):
   - Use NSIS, Inno Setup, or similar tools
   - Create proper Windows installer
   - Handle registry entries and shortcuts

## Troubleshooting

### Common Build Issues

#### 1. Missing Dependencies
**Error**: `ModuleNotFoundError` during build
**Solution**: 
- Add missing modules to `HIDDEN_IMPORTS` in `build_exe.py`
- Verify all requirements are installed

#### 1a. Build Dependency Installation Issues
**Error**: `ERROR: Could not find a version that satisfies the requirement` or Python version conflicts
**Solutions**:
- Use the automated installer: `python install_build_deps.py`
- Install only PyInstaller: `pip install pyinstaller>=5.0.0`
- Update pip first: `python -m pip install --upgrade pip`
- Check Python version compatibility (3.8+ required)
- For Python 3.12+, some packages may have limited versions available

#### 2. Large Executable Size
**Issue**: Executable is very large (>500MB)
**Solutions**:
- Enable UPX compression (requires separate UPX installation)
- Exclude unnecessary modules in the spec file
- Use `--exclude-module` for unused libraries

#### 3. Slow Startup
**Issue**: Executable takes long to start
**Solutions**:
- Use `--onedir` instead of `--onefile` (already default)
- Exclude debug symbols with `--strip`
- Consider lazy imports in your code

#### 4. Missing Data Files
**Error**: Application can't find configuration or data files
**Solution**:
- Verify all data files are listed in `DATA_FILES` and `DATA_DIRS`
- Check file paths are relative to the executable

#### 5. API/Network Issues
**Error**: Network requests fail in executable
**Solutions**:
- Ensure certificates are included
- Check firewall/antivirus settings
- Verify API keys are properly loaded

### Runtime Issues

#### 1. Application Won't Start
**Debugging steps**:
1. Run from command prompt to see error messages
2. Check `app.log` for detailed errors
3. Verify all files are in the same directory
4. Test on a clean Windows system

#### 2. Missing Features
**Common causes**:
- API keys not configured
- Missing data files
- Incorrect file permissions

#### 3. Performance Issues
**Solutions**:
- Ensure adequate system resources
- Clear cache directory if corrupted
- Check disk space availability

## Advanced Configuration

### Custom Icon
1. Create or obtain a `.ico` file
2. Place it at `icons/app_icon.ico`
3. The build script will automatically include it

### Version Information
Modify the `BUILD_CONFIG` in `build_exe.py`:
```python
BUILD_CONFIG = {
    "app_name": "MAIAChat",
    "version": "1.0.0",
    "description": "Your custom description",
    "company": "Your Company",
    "copyright": "© 2024 Your Company"
}
```

### Excluding Modules
To reduce size, exclude unnecessary modules:
```python
excludes=[
    'tkinter',
    'matplotlib',
    'IPython',
    'jupyter',
    'notebook',
    'your_unused_module'
]
```

### Adding Custom Data
Add your own data files or directories:
```python
DATA_DIRS.append("your_custom_directory")
DATA_FILES.append("your_custom_file.json")
```

## Performance Optimization

### Build Time Optimization
- Use SSD for build process
- Close unnecessary applications
- Use `--clean` flag sparingly (only when needed)

### Runtime Optimization
- Enable UPX compression for smaller size
- Use `--strip` to remove debug symbols
- Consider lazy loading for large modules

### Memory Optimization
- Monitor memory usage during build
- Use `--exclude-module` for unused libraries
- Consider splitting large modules

## Security Considerations

### API Keys
- Ensure API keys are not hardcoded
- Use secure storage mechanisms
- Provide clear instructions for key configuration

### Code Protection
- PyInstaller provides basic obfuscation
- For additional protection, consider:
  - Code signing certificates
  - Additional obfuscation tools
  - License verification systems

## Maintenance

### Updates
1. **Backup user data** before updates
2. **Replace application files** with new version
3. **Preserve user configurations** and data
4. **Test thoroughly** after updates

### Version Control
- Tag releases in your repository
- Maintain changelog
- Document breaking changes

## Support

### User Support
- Include comprehensive `USER_GUIDE.txt`
- Provide troubleshooting steps
- Create FAQ for common issues

### Developer Support
- Document build process changes
- Maintain build script updates
- Test on multiple Windows versions

## Conclusion

This build process creates a professional, distributable executable for your MAIAChat Desktop application. The automated build script handles most complexities, but understanding the underlying process helps with troubleshooting and customization.

For questions or issues, refer to:
- PyInstaller documentation: https://pyinstaller.org/
- This build guide
- Application logs and error messages

Remember: Always test your executable on a clean system before distribution! 